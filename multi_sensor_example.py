#!/usr/bin/env python3
"""
Example usage of the multi-sensor temperature and humidity plotting functionality.

This script demonstrates the main use cases for plotting sensor data from multiple
temperature and humidity sensors.
"""

from myutils.Th14 import plot_multiple_sensors_temperature_and_humidity

def main():
    """Main example demonstrating multi-sensor plotting."""
    
    # Define the sensor IDs you want to plot
    sensor_ids = ['IPQ_TH14', 'IPQ_TH11']
    
    print("Multi-Sensor Temperature and Humidity Plotting Example")
    print("="*55)
    print(f"Plotting data for sensors: {sensor_ids}")
    
    # Example 1: Plot both temperature and humidity data
    print("\nExample 1: Plotting both temperature and humidity data")
    print("-" * 50)
    
    data = plot_multiple_sensors_temperature_and_humidity(
        sensor_ids=sensor_ids,
        plot_temperature=True,
        plot_humidity=True,
        figsize=(15, 10)
    )
    
    # Print summary of what was retrieved
    print("Data retrieved:")
    for sensor_measurement, df in data.items():
        if not df.empty:
            print(f"  {sensor_measurement}: {len(df)} data points")
        else:
            print(f"  {sensor_measurement}: No data available")
    
    # Example 2: Plot only temperature data for comparison
    print("\nExample 2: Temperature data only")
    print("-" * 30)
    
    temp_data = plot_multiple_sensors_temperature_and_humidity(
        sensor_ids=sensor_ids,
        plot_temperature=True,
        plot_humidity=False,
        figsize=(12, 6)
    )
    
    # Example 3: Custom time range (last 24 hours)
    print("\nExample 3: Custom time range (specific day)")
    print("-" * 40)
    
    custom_data = plot_multiple_sensors_temperature_and_humidity(
        sensor_ids=sensor_ids,
        start_datetime='2025-08-11T00:00:00Z',
        stop_datetime='2025-08-12T00:00:00Z',
        plot_temperature=True,
        plot_humidity=True,
        figsize=(15, 8)
    )
    
    # Example 4: Save plot to file
    print("\nExample 4: Saving plot to file")
    print("-" * 30)
    
    plot_multiple_sensors_temperature_and_humidity(
        sensor_ids=sensor_ids,
        plot_temperature=True,
        plot_humidity=True,
        figsize=(15, 10),
        save_plot=True,
        filename="my_sensor_comparison.png"
    )
    
    print("\nAll examples completed!")
    print("Check the generated plots and saved files.")


def single_sensor_example():
    """Example showing how to use the function with a single sensor."""
    
    print("\n" + "="*55)
    print("Single Sensor Example (Backward Compatibility)")
    print("="*55)
    
    # You can still use the function with just one sensor
    single_sensor_data = plot_multiple_sensors_temperature_and_humidity(
        sensor_ids=['IPQ_TH14'],  # Just one sensor in the list
        plot_temperature=True,
        plot_humidity=True,
        figsize=(12, 8)
    )
    
    print("Single sensor plotting completed!")


def flexible_sensor_list_example():
    """Example showing how the function works with any number of sensors."""
    
    print("\n" + "="*55)
    print("Flexible Sensor List Example")
    print("="*55)
    
    # You can add as many sensors as you want to the list
    many_sensors = ['IPQ_TH14', 'IPQ_TH11']  # Add more sensor IDs here as needed
    
    print(f"Plotting data for {len(many_sensors)} sensors: {many_sensors}")
    
    multi_sensor_data = plot_multiple_sensors_temperature_and_humidity(
        sensor_ids=many_sensors,
        plot_temperature=True,
        plot_humidity=True,
        figsize=(16, 10)
    )
    
    print(f"Successfully plotted data for {len(many_sensors)} sensors!")


if __name__ == "__main__":
    # Run the main examples
    main()
    
    # Show single sensor usage
    single_sensor_example()
    
    # Show flexible sensor list usage
    flexible_sensor_list_example()
