# Multi-Sensor Temperature and Humidity Plotting

This document describes the new functionality added to `myutils/Th14.py` for plotting temperature and humidity data from multiple sensors on the same graph.

## Overview

The enhanced `Th14.py` module now includes functions that can:
- Retrieve data from multiple sensors simultaneously
- Plot temperature and humidity data on separate subplots
- Handle flexible sensor lists (any number of sensors)
- Provide clear legends and axis labels
- Support custom time ranges
- Save plots to files

## Main Functions

### `plot_multiple_sensors_temperature_and_humidity()`

This is the main function for plotting data from multiple sensors.

**Parameters:**
- `sensor_ids` (list): List of sensor device names (e.g., `['IPQ_TH14', 'IPQ_TH11']`)
- `start_datetime` (str, optional): Start time in ISO format (default: '2025-08-07T00:00:00Z')
- `stop_datetime` (str, optional): Stop time in ISO format (default: current time)
- `plot_temperature` (bool, optional): Whether to plot temperature data (default: True)
- `plot_humidity` (bool, optional): Whether to plot humidity data (default: True)
- `figsize` (tuple, optional): Figure size (width, height) in inches (default: (15, 10))
- `save_plot` (bool, optional): Whether to save the plot to file (default: False)
- `filename` (str, optional): Filename for saved plot (default: auto-generated)

**Returns:**
- `dict`: Dictionary containing the retrieved data for each sensor and measurement type

### `retrieve_sensor_data_for_measurement_type()`

This function retrieves data for a specific sensor and measurement type.

**Parameters:**
- `sensor_id` (str): The sensor device name (e.g., 'IPQ_TH14')
- `measurement_type` (str): The measurement type ('temperature' or 'humidity')
- `start_datetime` (str, optional): Start time in ISO format
- `stop_datetime` (str, optional): Stop time in ISO format

**Returns:**
- `pandas.DataFrame`: DataFrame with time index and 'value' column

## Usage Examples

### Basic Usage - Multiple Sensors

```python
from myutils.Th14 import plot_multiple_sensors_temperature_and_humidity

# Define sensor IDs
sensor_ids = ['IPQ_TH14', 'IPQ_TH11']

# Plot both temperature and humidity data
data = plot_multiple_sensors_temperature_and_humidity(
    sensor_ids=sensor_ids,
    plot_temperature=True,
    plot_humidity=True,
    figsize=(15, 10)
)
```

### Temperature Only

```python
# Plot only temperature data
temp_data = plot_multiple_sensors_temperature_and_humidity(
    sensor_ids=['IPQ_TH14', 'IPQ_TH11'],
    plot_temperature=True,
    plot_humidity=False,
    figsize=(12, 6)
)
```

### Custom Time Range

```python
# Plot data for a specific time range
custom_data = plot_multiple_sensors_temperature_and_humidity(
    sensor_ids=['IPQ_TH14', 'IPQ_TH11'],
    start_datetime='2025-08-11T00:00:00Z',
    stop_datetime='2025-08-12T00:00:00Z',
    plot_temperature=True,
    plot_humidity=True
)
```

### Save Plot to File

```python
# Save the plot to a file
plot_multiple_sensors_temperature_and_humidity(
    sensor_ids=['IPQ_TH14', 'IPQ_TH11'],
    plot_temperature=True,
    plot_humidity=True,
    save_plot=True,
    filename="sensor_comparison.png"
)
```

### Single Sensor (Backward Compatibility)

```python
# Works with just one sensor too
single_data = plot_multiple_sensors_temperature_and_humidity(
    sensor_ids=['IPQ_TH14'],  # Just one sensor in the list
    plot_temperature=True,
    plot_humidity=True
)
```

### Flexible Number of Sensors

```python
# Add as many sensors as needed
many_sensors = ['IPQ_TH14', 'IPQ_TH11', 'IPQ_TH12', 'IPQ_TH13']  # Example

data = plot_multiple_sensors_temperature_and_humidity(
    sensor_ids=many_sensors,
    plot_temperature=True,
    plot_humidity=True,
    figsize=(16, 12)  # Larger figure for more sensors
)
```

## Features

### Visual Features
- **Clear Legends**: Each sensor is identified with a unique color and label
- **Separate Subplots**: Temperature and humidity data are plotted on separate subplots for clarity
- **Time Formatting**: X-axis shows both time (HH:MM) and date (MM-DD) labels
- **Day Overlays**: Semi-transparent alternating day backgrounds for easy time reference
- **Grid Lines**: Light grid lines for better data reading

### Data Handling
- **Automatic Data Retrieval**: Connects to InfluxDB and retrieves data automatically
- **Error Handling**: Graceful handling of missing data or connection issues
- **Data Aggregation**: 1-minute averaging for smoother plots
- **Flexible Time Ranges**: Support for custom start and stop times

### Output Options
- **Interactive Display**: Shows plots in matplotlib windows
- **File Saving**: Option to save plots as PNG files with high resolution
- **Data Return**: Returns retrieved data for further analysis

## Requirements

Before using this functionality, ensure you have the required dependencies installed:

```bash
pip install influxdb-client pandas matplotlib numpy
```

## Files

- `myutils/Th14.py` - Main module with the plotting functions
- `test_multi_sensor_plot.py` - Comprehensive test script
- `multi_sensor_example.py` - Usage examples
- `README_multi_sensor_plotting.md` - This documentation

## Error Handling

The functions include comprehensive error handling:
- Missing sensor data is reported with warnings
- Connection errors are caught and reported
- Invalid parameters raise appropriate exceptions
- Empty datasets are handled gracefully

## Backward Compatibility

The new functions are fully backward compatible. The original single-sensor plotting code in `Th14.py` has been replaced with example usage, but all existing functionality is preserved through the new multi-sensor function.
