#!/usr/bin/env python3
"""
Test script for the multi-sensor temperature and humidity plotting functionality.

This script demonstrates how to use the plot_multiple_sensors_temperature_and_humidity function
to visualize data from multiple sensors on the same graph.
"""

from myutils.Th14 import plot_multiple_sensors_temperature_and_humidity, retrieve_sensor_data_for_measurement_type

def test_multi_sensor_plotting():
    """Test the multi-sensor plotting functionality with different configurations."""
    
    # Define sensor IDs to test
    sensor_ids = ['IPQ_TH14', 'IPQ_TH11']
    
    print("="*60)
    print("Testing Multi-Sensor Temperature and Humidity Plotting")
    print("="*60)
    
    # Test 1: Plot both temperature and humidity for multiple sensors
    print("\nTest 1: Plotting both temperature and humidity for multiple sensors")
    print(f"Sensors: {sensor_ids}")
    
    try:
        data = plot_multiple_sensors_temperature_and_humidity(
            sensor_ids=sensor_ids,
            plot_temperature=True,
            plot_humidity=True,
            figsize=(15, 10)
        )
        
        print("✓ Successfully plotted temperature and humidity data")
        
        # Print data summary
        print("\nData Summary:")
        for key, df in data.items():
            if not df.empty:
                print(f"  {key}: {len(df)} data points")
                print(f"    Time range: {df.index.min()} to {df.index.max()}")
                print(f"    Value range: {df['value'].min():.2f} to {df['value'].max():.2f}")
            else:
                print(f"  {key}: No data available")
        
    except Exception as e:
        print(f"✗ Error in Test 1: {e}")
    
    # Test 2: Plot only temperature data
    print("\n" + "-"*50)
    print("Test 2: Plotting only temperature data")
    
    try:
        temp_data = plot_multiple_sensors_temperature_and_humidity(
            sensor_ids=sensor_ids,
            plot_temperature=True,
            plot_humidity=False,
            figsize=(12, 6)
        )
        print("✓ Successfully plotted temperature-only data")
        
    except Exception as e:
        print(f"✗ Error in Test 2: {e}")
    
    # Test 3: Plot only humidity data
    print("\n" + "-"*50)
    print("Test 3: Plotting only humidity data")
    
    try:
        hum_data = plot_multiple_sensors_temperature_and_humidity(
            sensor_ids=sensor_ids,
            plot_temperature=False,
            plot_humidity=True,
            figsize=(12, 6)
        )
        print("✓ Successfully plotted humidity-only data")
        
    except Exception as e:
        print(f"✗ Error in Test 3: {e}")
    
    # Test 4: Single sensor (backward compatibility)
    print("\n" + "-"*50)
    print("Test 4: Single sensor plotting (backward compatibility)")
    
    try:
        single_data = plot_multiple_sensors_temperature_and_humidity(
            sensor_ids=['IPQ_TH14'],
            plot_temperature=True,
            plot_humidity=True,
            figsize=(12, 8)
        )
        print("✓ Successfully plotted single sensor data")
        
    except Exception as e:
        print(f"✗ Error in Test 4: {e}")
    
    # Test 5: Test individual data retrieval function
    print("\n" + "-"*50)
    print("Test 5: Testing individual data retrieval function")
    
    try:
        # Test temperature data retrieval
        temp_df = retrieve_sensor_data_for_measurement_type('IPQ_TH14', 'temperature')
        print(f"✓ Retrieved temperature data: {len(temp_df)} points")
        
        # Test humidity data retrieval
        hum_df = retrieve_sensor_data_for_measurement_type('IPQ_TH14', 'humidity')
        print(f"✓ Retrieved humidity data: {len(hum_df)} points")
        
    except Exception as e:
        print(f"✗ Error in Test 5: {e}")
    
    print("\n" + "="*60)
    print("Testing completed!")
    print("="*60)


def test_custom_time_range():
    """Test plotting with a custom time range."""
    
    print("\n" + "="*60)
    print("Testing Custom Time Range")
    print("="*60)
    
    # Define a shorter time range for faster testing
    start_time = '2025-08-11T00:00:00Z'
    stop_time = '2025-08-12T00:00:00Z'
    
    sensor_ids = ['IPQ_TH14', 'IPQ_TH11']
    
    try:
        data = plot_multiple_sensors_temperature_and_humidity(
            sensor_ids=sensor_ids,
            start_datetime=start_time,
            stop_datetime=stop_time,
            plot_temperature=True,
            plot_humidity=True,
            figsize=(15, 8)
        )
        
        print(f"✓ Successfully plotted data for time range: {start_time} to {stop_time}")
        
        # Print data summary
        print("\nCustom Time Range Data Summary:")
        for key, df in data.items():
            if not df.empty:
                print(f"  {key}: {len(df)} data points")
            else:
                print(f"  {key}: No data available")
        
    except Exception as e:
        print(f"✗ Error in custom time range test: {e}")


if __name__ == "__main__":
    # Run the main tests
    test_multi_sensor_plotting()
    
    # Run custom time range test
    test_custom_time_range()
    
    print("\nAll tests completed! Check the generated plots.")
